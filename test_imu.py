import pandas as pd
import os

# Test IMU feature detection
def test_imu_detection():
    print("🔍 Testing IMU feature detection...")
    
    # Load a sample file
    sample_file = "datasets/AB2930/AB2930/Features/AB2930_Trial_001_feat_0ms.csv"
    
    if os.path.exists(sample_file):
        df = pd.read_csv(sample_file)
        print(f"📊 Total columns: {len(df.columns)}")
        
        # Find EMG features
        emg_cols = [col for col in df.columns if 'EMG' in col]
        print(f"🧠 EMG columns found: {len(emg_cols)}")
        print(f"Sample EMG: {emg_cols[:3]}")
        
        # Find IMU features
        imu_cols = [col for col in df.columns if 'ACC' in col or 'GYRO' in col]
        print(f"📱 IMU columns found: {len(imu_cols)}")
        print(f"Sample IMU: {imu_cols[:5]}")
        
        # Find specific IMU patterns
        acc_mean = [col for col in df.columns if 'ACC' in col and 'mean' in col]
        gyro_std = [col for col in df.columns if 'GYRO' in col and 'std_dev' in col]
        
        print(f"📈 ACC mean features: {len(acc_mean)}")
        print(f"📉 GYRO std_dev features: {len(gyro_std)}")
        
        if len(acc_mean) > 0:
            print(f"Sample ACC mean: {acc_mean[:3]}")
        if len(gyro_std) > 0:
            print(f"Sample GYRO std: {gyro_std[:3]}")
            
        return len(emg_cols), len(imu_cols)
    else:
        print(f"❌ File not found: {sample_file}")
        return 0, 0

if __name__ == "__main__":
    emg_count, imu_count = test_imu_detection()
    print(f"\n✅ Summary: {emg_count} EMG, {imu_count} IMU features detected")
