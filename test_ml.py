import os
import pandas as pd
import numpy as np

print("Testing basic imports...")

# Test data loading
subjects = ["AB2930", "AB2931", "AB2932"]

def test_data_loading():
    print("Testing data loading...")
    
    for subject in subjects:
        features_path = os.path.join("datasets", subject, subject, "Features")
        if os.path.exists(features_path):
            print(f"Found features for {subject}")
            files = os.listdir(features_path)
            print(f"  Files: {len(files)}")
            if files:
                # Try to load one file
                try:
                    sample_file = os.path.join(features_path, files[0])
                    df = pd.read_csv(sample_file)
                    print(f"  Sample file shape: {df.shape}")
                    print(f"  Columns: {list(df.columns)[:10]}...")
                    break
                except Exception as e:
                    print(f"  Error loading file: {e}")
        else:
            print(f"No features found for {subject}")

if __name__ == "__main__":
    test_data_loading()
    print("Test completed!")
