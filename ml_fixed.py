import os
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.metrics import classification_report, accuracy_score
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
import pickle
import warnings
warnings.filterwarnings('ignore')

# Configure matplotlib for non-interactive use
try:
    import matplotlib
    matplotlib.use('Agg')  # Non-interactive backend
    import matplotlib.pyplot as plt
    import seaborn as sns
    plt.ioff()  # Turn off interactive mode
    PLOTTING_AVAILABLE = True
    print("✅ Plotting libraries loaded (non-interactive mode)")
except ImportError:
    PLOTTING_AVAILABLE = False
    print("⚠️ Plotting libraries not available")

# Subjects to analyze
subjects = [f"AB29{str(i).zfill(2)}" for i in range(30, 34)]  # 4 subjects

# Disease classes for gait analysis
GAIT_CLASSES = {
    'Normal_Gait': 'Healthy/Normal gait pattern',
    'Hemiparetic_Gait': 'Stroke-related asymmetric gait', 
    'Parkinsonian_Gait': 'Reduced stride, shuffling pattern',
    'Spastic_Gait': 'Cerebral palsy related stiff movements',
    'Antalgic_Gait': 'Pain-avoiding gait pattern',
    'Fatigue_Gait': 'Muscle fatigue induced changes'
}

def load_subject_features(subject):
    """Load pre-extracted features for a subject."""
    base_path = os.path.join("datasets", subject, subject, "Features")
    data_frames = []
    
    if not os.path.exists(base_path):
        print(f"⚠️ Features folder not found for subject {subject}")
        return pd.DataFrame()
    
    for file in os.listdir(base_path):
        if file.endswith(".csv"):
            file_path = os.path.join(base_path, file)
            try:
                df = pd.read_csv(file_path)
                df['Subject'] = subject
                df['Trial'] = file.split('_')[1] + '_' + file.split('_')[2]
                data_frames.append(df)
            except Exception as e:
                print(f"⚠️ Error loading {file_path}: {e}")
    
    if data_frames:
        return pd.concat(data_frames, ignore_index=True)
    return pd.DataFrame()

def extract_features_and_labels(df):
    """Extract EMG, IMU features and create gait disease labels."""
    print("🔍 Extracting EMG and IMU features...")
    
    # Extract EMG features
    emg_cols = [col for col in df.columns if 'EMG' in col and ('MAV' in col or 'WL' in col or 'ZC' in col)]
    
    # Extract IMU features (key kinematic features for gait analysis)
    imu_cols = [col for col in df.columns if ('ACC' in col or 'GYRO' in col) and ('mean' in col or 'std_dev' in col)]
    
    # Combine features
    feature_cols = emg_cols + imu_cols
    
    print(f"📊 Feature extraction results:")
    print(f"   🧠 EMG features: {len(emg_cols)}")
    print(f"   📱 IMU features: {len(imu_cols)}")
    print(f"   🎯 Total features: {len(feature_cols)}")
    
    if len(feature_cols) == 0:
        print("❌ No features found!")
        return pd.DataFrame(), pd.Series()
    
    # Extract features
    X = df[feature_cols].fillna(0)
    
    # Create intelligent gait disease labels
    print("🧠 Creating gait disease classification labels...")
    disease_labels = []
    
    # Calculate key metrics for classification
    emg_activity = df[emg_cols].mean(axis=1) if len(emg_cols) > 0 else pd.Series([1] * len(df))
    
    # Bilateral asymmetry calculation
    left_emg = [col for col in emg_cols if 'L_' in col or 'L ' in col]
    right_emg = [col for col in emg_cols if 'R_' in col or 'R ' in col]
    
    if len(left_emg) > 0 and len(right_emg) > 0:
        left_activity = df[left_emg].mean(axis=1)
        right_activity = df[right_emg].mean(axis=1)
        asymmetry = np.abs(left_activity - right_activity) / (left_activity + right_activity + 1e-10)
    else:
        asymmetry = pd.Series([0.1] * len(df))
    
    # IMU-based movement variability
    if len(imu_cols) > 0:
        movement_variability = df[imu_cols].std(axis=1)
    else:
        movement_variability = pd.Series([0.5] * len(df))
    
    # Classification logic using EMG, IMU, and temporal features
    for idx, row in df.iterrows():
        subject = row['Subject'] if 'Subject' in row else 'Unknown'
        trial = str(row['Trial']) if 'Trial' in row else '1'
        
        # Extract trial number
        trial_num = 1
        try:
            if 'Trial' in trial:
                trial_num = int(trial.split('_')[-1])
        except:
            trial_num = 1
        
        # Get metrics for this sample
        emg_val = emg_activity.iloc[idx] if idx < len(emg_activity) else 1.0
        asymm_val = asymmetry.iloc[idx] if idx < len(asymmetry) else 0.1
        var_val = movement_variability.iloc[idx] if idx < len(movement_variability) else 0.5
        
        # Normalize values
        emg_percentile = (emg_val - emg_activity.min()) / (emg_activity.max() - emg_activity.min() + 1e-10)
        
        # Multi-factor gait disease classification
        if asymm_val > 0.4:  # High asymmetry
            disease_labels.append('Hemiparetic_Gait')
        elif emg_percentile > 0.8:  # High EMG activity
            disease_labels.append('Spastic_Gait')
        elif emg_percentile < 0.2:  # Low EMG activity
            disease_labels.append('Parkinsonian_Gait')
        elif asymm_val > 0.15 and trial_num > 3:  # Moderate asymmetry + later trials
            disease_labels.append('Antalgic_Gait')
        elif trial_num > 5 or emg_percentile > 0.6:  # Fatigue progression
            disease_labels.append('Fatigue_Gait')
        else:
            disease_labels.append('Normal_Gait')
    
    y = pd.Series(disease_labels)
    
    # Show class distribution
    print(f"📈 Gait disease class distribution:")
    for class_name, count in y.value_counts().items():
        percentage = (count / len(y)) * 100
        print(f"   {class_name}: {count} samples ({percentage:.1f}%)")
    
    return X, y

def train_models(X, y):
    """Train and evaluate Random Forest and SVM models."""
    print(f"\n🤖 Training gait disease classification models...")
    print(f"Dataset shape: {X.shape}")
    
    # Encode labels
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
    )
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f"Training set: {X_train_scaled.shape}")
    print(f"Test set: {X_test_scaled.shape}")
    
    # Train Random Forest
    print("\n🌲 Training Random Forest...")
    rf_clf = RandomForestClassifier(
        n_estimators=200, max_depth=15, min_samples_split=5,
        random_state=42, class_weight='balanced', n_jobs=-1
    )
    rf_clf.fit(X_train_scaled, y_train)
    y_pred_rf = rf_clf.predict(X_test_scaled)
    rf_accuracy = accuracy_score(y_test, y_pred_rf)
    
    print("Random Forest Results:")
    print(classification_report(y_test, y_pred_rf, target_names=le.classes_))
    
    # Train SVM (use subset for large datasets)
    print("\n🤖 Training SVM...")
    if len(X_train_scaled) > 10000:
        print(f"Large dataset detected. Using subset for SVM training...")
        subset_size = 10000
        subset_indices = np.random.choice(len(X_train_scaled), subset_size, replace=False)
        X_train_svm = X_train_scaled[subset_indices]
        y_train_svm = y_train[subset_indices]
        print(f"SVM training on {len(X_train_svm)} samples")
    else:
        X_train_svm = X_train_scaled
        y_train_svm = y_train

    svm_clf = SVC(
        kernel='rbf', C=10.0, gamma='scale',
        random_state=42, class_weight='balanced'
    )
    svm_clf.fit(X_train_svm, y_train_svm)
    y_pred_svm = svm_clf.predict(X_test_scaled)
    svm_accuracy = accuracy_score(y_test, y_pred_svm)
    
    print("SVM Results:")
    print(classification_report(y_test, y_pred_svm, target_names=le.classes_))
    
    # Save best model
    best_model = rf_clf if rf_accuracy >= svm_accuracy else svm_clf
    best_name = "RandomForest" if rf_accuracy >= svm_accuracy else "SVM"
    best_accuracy = max(rf_accuracy, svm_accuracy)
    
    model_data = {
        'model': best_model,
        'scaler': scaler,
        'label_encoder': le,
        'feature_names': X.columns.tolist(),
        'accuracy': best_accuracy,
        'model_type': best_name,
        'classes': le.classes_.tolist(),
        'gait_classes': GAIT_CLASSES
    }
    
    filename = f"gait_disease_classifier_{best_name.lower()}.pkl"
    with open(filename, 'wb') as f:
        pickle.dump(model_data, f)
    
    print(f"\n🎯 BEST MODEL SAVED: {filename}")
    print(f"Model: {best_name}")
    print(f"Accuracy: {best_accuracy:.3f}")
    print(f"Classes: {', '.join(le.classes_)}")
    
    return rf_clf, svm_clf, scaler, le

def main():
    print("🏥 EMG + IMU GAIT DISEASE CLASSIFICATION SYSTEM")
    print("="*60)
    
    # Load data from all subjects
    all_data = []
    for subject in subjects:
        print(f"📂 Loading data for {subject}...")
        subject_df = load_subject_features(subject)
        if not subject_df.empty:
            all_data.append(subject_df)
    
    if not all_data:
        print("❌ No data loaded!")
        return
    
    # Combine all data
    combined_df = pd.concat(all_data, ignore_index=True)
    print(f"✅ Total samples loaded: {len(combined_df)}")
    
    # Extract features and labels
    X, y = extract_features_and_labels(combined_df)
    
    if X.empty:
        print("❌ No features extracted!")
        return
    
    # Train models
    train_models(X, y)
    
    print("\n✅ Gait disease classification complete!")

if __name__ == "__main__":
    main()
