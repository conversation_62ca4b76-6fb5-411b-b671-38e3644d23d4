import os
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
import pickle
import warnings
warnings.filterwarnings('ignore')

# List of subjects to load (start with fewer subjects to avoid memory issues)
subjects = [f"AB29{str(i).zfill(2)}" for i in range(30, 35)]  # Use 5 subjects for better generalization

# EMG feature types for fatigue detection
emg_feature_types = ['MAV', 'WL', 'ZC', 'SS', 'AR coeff1', 'AR coeff2', 'AR coeff3', 'AR coeff4', 'AR coeff5', 'AR coeff6']

# List of muscles for bilateral comparison and fatigue detection
target_muscles = [
    "R_rectus femoris", "L_Rectus Femoris",
    "R_Vastus Lateralis", "L_Vastus Lateralis",
    "R_Semitendinosus", "L_Semitendinosus",
    "L_Biceps_Femoris",
    "R_Medial Gastrocnemius", "L_Medial Gastrocnemius",
    "R_Soleus", "L_Soleus",
    "R_Tibialis Anterior", "L_Tibialis Anterior",
    "R_Extensor Digitorum Brevis", "L_Extensor Digitorum Brevis"
]

def load_subject_features(subject):
    """
    Load pre-extracted features from Features folder for a subject.
    Returns a single DataFrame with all features for the subject.
    """
    base_path = os.path.join("datasets", subject, subject, "Features")
    data_frames = []

    if not os.path.exists(base_path):
        print(f"Warning: Features folder not found for subject {subject}")
        return pd.DataFrame()

    # Load all feature files
    for file in os.listdir(base_path):
        if file.endswith(".csv"):
            file_path = os.path.join(base_path, file)
            try:
                df = pd.read_csv(file_path)
                # Add trial information from filename
                trial_info = file.split('_')
                if len(trial_info) >= 3:
                    df['Trial'] = trial_info[1] + '_' + trial_info[2]
                    df['Subject'] = subject
                data_frames.append(df)
            except Exception as e:
                print(f"Warning: Could not read {file_path}: {e}")

    if data_frames:
        subject_df = pd.concat(data_frames, ignore_index=True)
        return subject_df
    else:
        return pd.DataFrame()

def load_subject_raw_data(subject, max_trials=5):
    """
    Load raw EMG data from Raw Data folder for a subject (limited trials to manage memory).
    Returns a DataFrame with raw EMG signals and labels.
    """
    base_path = os.path.join("datasets", subject, subject, "Raw Data")
    data_frames = []

    if not os.path.exists(base_path):
        print(f"Warning: Raw Data folder not found for subject {subject}")
        return pd.DataFrame()

    # Load limited number of trials to avoid memory issues
    trial_count = 0
    for file in sorted(os.listdir(base_path)):
        if file.endswith("_Analog_raw.csv") and trial_count < max_trials:
            file_path = os.path.join(base_path, file)
            try:
                df = pd.read_csv(file_path)
                # Add trial and subject information
                trial_info = file.split('_')
                if len(trial_info) >= 3:
                    df['Trial'] = trial_info[1] + '_' + trial_info[2]
                    df['Subject'] = subject
                data_frames.append(df)
                trial_count += 1
            except Exception as e:
                print(f"Warning: Could not read {file_path}: {e}")

    if data_frames:
        subject_df = pd.concat(data_frames, ignore_index=True)
        return subject_df
    else:
        return pd.DataFrame()

def extract_emg_features_from_features_data(df):
    """
    Extract EMG features from pre-computed features data for fatigue detection.
    Focus on bilateral muscle comparison and fatigue-related features.
    """
    feature_columns = []

    # Extract EMG features for each target muscle
    for muscle in target_muscles:
        for feature_type in emg_feature_types:
            # Try different naming conventions
            possible_names = [
                f"{muscle}_EMG 1 {feature_type}",
                f"{muscle}_EMG1 {feature_type}",
                f"{muscle} EMG 1 {feature_type}",
                f"{muscle} EMG1 {feature_type}"
            ]

            for name in possible_names:
                if name in df.columns:
                    feature_columns.append(name)
                    break

    print(f"Found {len(feature_columns)} EMG features out of {len(target_muscles) * len(emg_feature_types)} expected")

    if len(feature_columns) == 0:
        print("No EMG features found! Available columns:")
        print([col for col in df.columns if 'EMG' in col][:10])  # Show first 10 EMG columns
        return pd.DataFrame(), pd.Series()

    # Extract features
    X = df[feature_columns].copy()

    # Handle missing values
    X = X.fillna(0)

    # Extract target labels
    if 'Mode' in df.columns:
        y = df['Mode']
    elif 'phase' in df.columns:
        y = df['phase']
    else:
        print("Warning: No target labels found. Creating dummy labels.")
        y = pd.Series([1] * len(df))  # Dummy labels

    # Remove rows with missing targets
    valid_idx = y.notna()
    X = X.loc[valid_idx].reset_index(drop=True)
    y = y.loc[valid_idx].reset_index(drop=True)

    return X, y

def extract_emg_features_from_raw_data(df):
    """
    Extract EMG features from raw EMG signals for fatigue detection.
    """
    emg_columns = []

    # Find EMG signal columns
    for muscle in target_muscles:
        possible_names = [
            f"{muscle}_EMG 1",
            f"{muscle}_EMG1",
            f"{muscle} EMG 1",
            f"{muscle} EMG1"
        ]

        for name in possible_names:
            if name in df.columns:
                emg_columns.append(name)
                break

    print(f"Found {len(emg_columns)} raw EMG signals")

    if len(emg_columns) == 0:
        print("No raw EMG signals found! Available columns:")
        print([col for col in df.columns if 'EMG' in col][:10])
        return pd.DataFrame(), pd.Series()

    # Simple feature extraction: use raw EMG values as features
    # In practice, you'd extract features like RMS, mean frequency, etc.
    X = df[emg_columns].copy()
    X = X.fillna(0)

    # Extract target labels
    if 'Mode' in df.columns:
        y = df['Mode']
    elif 'phase' in df.columns:
        y = df['phase']
    else:
        print("Warning: No target labels found. Creating dummy labels.")
        y = pd.Series([1] * len(df))

    # Remove rows with missing targets
    valid_idx = y.notna()
    X = X.loc[valid_idx].reset_index(drop=True)
    y = y.loc[valid_idx].reset_index(drop=True)

    return X, y

def create_fatigue_labels_from_features(df, X):
    """
    Create more realistic fatigue labels based on EMG feature analysis.
    Uses temporal progression and EMG characteristics to infer fatigue.
    """
    print("Creating intelligent fatigue labels based on EMG characteristics...")

    # Method 1: Use trial progression within each subject
    if 'Trial' in df.columns and 'Subject' in df.columns:
        fatigue_labels = []
        for subject in df['Subject'].unique():
            subject_data = df[df['Subject'] == subject]
            trials = sorted(subject_data['Trial'].unique())

            # Early trials = Non-Fatigued, Later trials = Fatigued
            mid_point = len(trials) // 2
            early_trials = trials[:mid_point]
            late_trials = trials[mid_point:]

            for _, row in subject_data.iterrows():
                if row['Trial'] in early_trials:
                    fatigue_labels.append('Non-Fatigued')
                else:
                    fatigue_labels.append('Fatigued')

        return pd.Series(fatigue_labels)

    # Method 2: Use EMG amplitude changes (fatigue typically shows increased amplitude)
    # Calculate mean EMG activity for each sample
    emg_cols = [col for col in X.columns if 'MAV' in col or 'EMG' in col]
    if len(emg_cols) > 0:
        mean_emg_activity = X[emg_cols].mean(axis=1)
        # Higher activity in later samples suggests fatigue
        threshold = mean_emg_activity.median()
        fatigue_labels = ['Fatigued' if activity > threshold else 'Non-Fatigued'
                         for activity in mean_emg_activity]
        return pd.Series(fatigue_labels)

    # Method 3: Fallback to temporal progression
    n_samples = len(X)
    fatigue_labels = ['Non-Fatigued'] * (n_samples//2) + ['Fatigued'] * (n_samples - n_samples//2)
    return pd.Series(fatigue_labels)

def detect_asymmetry_features(X):
    """
    Create asymmetry features by comparing bilateral muscle pairs.
    """
    asymmetry_features = pd.DataFrame()

    # Define bilateral muscle pairs
    muscle_pairs = [
        ('R_rectus femoris', 'L_Rectus Femoris'),
        ('R_Vastus Lateralis', 'L_Vastus Lateralis'),
        ('R_Semitendinosus', 'L_Semitendinosus'),
        ('R_Medial Gastrocnemius', 'L_Medial Gastrocnemius'),
        ('R_Soleus', 'L_Soleus'),
        ('R_Tibialis Anterior', 'L_Tibialis Anterior'),
        ('R_Extensor Digitorum Brevis', 'L_Extensor Digitorum Brevis')
    ]

    for right_muscle, left_muscle in muscle_pairs:
        # Find corresponding EMG features
        right_cols = [col for col in X.columns if right_muscle in col and 'EMG' in col]
        left_cols = [col for col in X.columns if left_muscle in col and 'EMG' in col]

        for feature_type in emg_feature_types:
            right_col = None
            left_col = None

            # Find matching feature columns
            for col in right_cols:
                if feature_type in col:
                    right_col = col
                    break

            for col in left_cols:
                if feature_type in col:
                    left_col = col
                    break

            if right_col and left_col and right_col in X.columns and left_col in X.columns:
                # Calculate asymmetry ratio
                asymmetry_name = f"Asymmetry_{right_muscle.split('_')[1]}_{feature_type}"
                # Avoid division by zero
                left_vals = X[left_col].replace(0, 1e-10)
                asymmetry_features[asymmetry_name] = X[right_col] / left_vals

    print(f"Created {len(asymmetry_features.columns)} asymmetry features")
    return asymmetry_features

def train_and_evaluate_models(X, y, df=None, use_scaling=True):
    """
    Train and evaluate Random Forest and SVM models for EMG fatigue detection.
    """
    print(f"Dataset shape: {X.shape}")
    print(f"Target distribution:")
    print(y.value_counts())

    # Check if we have variation in target variable
    unique_targets = y.nunique()
    if unique_targets == 1:
        print(f"Warning: Only one unique target value found: {y.iloc[0]}")
        print("Cannot perform classification with only one class.")
        print("This suggests all data is from the same condition/phase.")
        print("Consider:")
        print("1. Loading data from different conditions/phases")
        print("2. Creating artificial labels based on time progression")
        print("3. Using unsupervised learning for pattern detection")

        # Create intelligent fatigue labels
        print("\nCreating intelligent fatigue labels...")
        y = create_fatigue_labels_from_features(df, X)

    # Encode labels if they are strings
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)

    print(f"Final target distribution after processing:")
    print(pd.Series(y).value_counts())

    # Check if we have enough samples for train/test split
    if len(X) < 10:
        print("Warning: Very small dataset. Results may not be reliable.")
        test_size = 0.3
    else:
        test_size = 0.2

    # Split data
    try:
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=test_size, random_state=42, stratify=y_encoded
        )
    except ValueError:
        # If stratification fails, split without stratification
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=test_size, random_state=42
        )

    # Scale features if requested
    if use_scaling:
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
    else:
        X_train_scaled = X_train
        X_test_scaled = X_test

    print(f"Training set size: {X_train_scaled.shape}")
    print(f"Test set size: {X_test_scaled.shape}")

    # Train Random Forest Classifier
    print("\n" + "="*50)
    print("Training Random Forest Classifier...")
    rf_clf = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        class_weight='balanced'  # Handle class imbalance
    )
    rf_clf.fit(X_train_scaled, y_train)
    y_pred_rf = rf_clf.predict(X_test_scaled)

    print("Random Forest Classification Report:")
    try:
        target_names = [str(cls) for cls in le.classes_]
        print(classification_report(y_test, y_pred_rf, target_names=target_names))
    except:
        print(classification_report(y_test, y_pred_rf))

    # Feature importance for Random Forest
    if hasattr(rf_clf, 'feature_importances_'):
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': rf_clf.feature_importances_
        }).sort_values('importance', ascending=False)
        print("\nTop 10 Most Important Features (Random Forest):")
        print(feature_importance.head(10))

    # Train Support Vector Machine Classifier (use subset for large datasets)
    print("\n" + "="*50)
    print("Training Support Vector Machine Classifier...")

    # Use subset for SVM if dataset is too large (SVM doesn't scale well)
    if len(X_train_scaled) > 10000:
        print(f"Large dataset detected ({len(X_train_scaled)} samples). Using subset for SVM training...")
        subset_size = 10000
        subset_indices = np.random.choice(len(X_train_scaled), subset_size, replace=False)
        X_train_svm = X_train_scaled[subset_indices]
        y_train_svm = y_train[subset_indices]
        print(f"SVM training on {len(X_train_svm)} samples")
    else:
        X_train_svm = X_train_scaled
        y_train_svm = y_train

    svm_clf = SVC(
        kernel='rbf',
        C=1.0,
        random_state=42,
        class_weight='balanced'
    )
    svm_clf.fit(X_train_svm, y_train_svm)
    y_pred_svm = svm_clf.predict(X_test_scaled)

    print("SVM Classification Report:")
    try:
        target_names = [str(cls) for cls in le.classes_]
        print(classification_report(y_test, y_pred_svm, target_names=target_names))
    except:
        print(classification_report(y_test, y_pred_svm))

    # Save the best performing model
    rf_accuracy = (y_pred_rf == y_test).mean()
    svm_accuracy = (y_pred_svm == y_test).mean()

    if rf_accuracy >= svm_accuracy:
        best_model = rf_clf
        best_model_name = "RandomForest"
        best_accuracy = rf_accuracy
    else:
        best_model = svm_clf
        best_model_name = "SVM"
        best_accuracy = svm_accuracy

    # Save the best model
    model_filename = f"emg_fatigue_model_{best_model_name.lower()}.pkl"
    model_data = {
        'model': best_model,
        'scaler': scaler if use_scaling else None,
        'label_encoder': le,
        'feature_names': X.columns.tolist(),
        'accuracy': best_accuracy,
        'model_type': best_model_name
    }

    with open(model_filename, 'wb') as f:
        pickle.dump(model_data, f)

    print(f"\nBest model ({best_model_name}) saved as '{model_filename}'")
    print(f"Model accuracy: {best_accuracy:.3f}")

    return rf_clf, svm_clf, scaler if use_scaling else None, le

def main():
    print("EMG Fatigue Detection ML Model")
    print("="*50)

    # Try loading pre-extracted features first
    print("Attempting to load pre-extracted features...")
    all_features_data = []

    for subject in subjects:
        print(f"Loading features for subject {subject}...")
        features_df = load_subject_features(subject)
        if not features_df.empty:
            all_features_data.append(features_df)
        else:
            print(f"No features found for subject {subject}")

    if all_features_data:
        print(f"\nLoaded features from {len(all_features_data)} subjects")
        features_combined = pd.concat(all_features_data, ignore_index=True)
        print(f"Total feature samples: {len(features_combined)}")

        # Extract EMG features and labels
        X_features, y_features = extract_emg_features_from_features_data(features_combined)

        if not X_features.empty:
            print("\n" + "="*50)
            print("TRAINING WITH PRE-EXTRACTED FEATURES")
            print("="*50)

            # Add asymmetry features for enhanced analysis
            asymmetry_features = detect_asymmetry_features(X_features)
            if not asymmetry_features.empty:
                X_enhanced = pd.concat([X_features, asymmetry_features], axis=1)
                print(f"Enhanced dataset with asymmetry features: {X_enhanced.shape}")
            else:
                X_enhanced = X_features

            train_and_evaluate_models(X_enhanced, y_features, features_combined)
        else:
            print("No valid EMG features extracted from features data.")

    # Also try loading raw data for comparison
    print("\n" + "="*50)
    print("Attempting to load raw EMG data...")
    all_raw_data = []

    for subject in subjects:
        print(f"Loading raw data for subject {subject}...")
        raw_df = load_subject_raw_data(subject, max_trials=3)  # Limit trials to manage memory
        if not raw_df.empty:
            all_raw_data.append(raw_df)
        else:
            print(f"No raw data found for subject {subject}")

    if all_raw_data:
        print(f"\nLoaded raw data from {len(all_raw_data)} subjects")
        raw_combined = pd.concat(all_raw_data, ignore_index=True)
        print(f"Total raw data samples: {len(raw_combined)}")

        # Extract EMG features and labels from raw data
        X_raw, y_raw = extract_emg_features_from_raw_data(raw_combined)

        if not X_raw.empty:
            print("\n" + "="*50)
            print("TRAINING WITH RAW EMG SIGNALS")
            print("="*50)
            train_and_evaluate_models(X_raw, y_raw, raw_combined)
        else:
            print("No valid EMG signals extracted from raw data.")

    if not all_features_data and not all_raw_data:
        print("No data could be loaded. Please check your dataset structure.")

if __name__ == "__main__":
    main()
