import os
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.metrics import classification_report
from sklearn.model_selection import train_test_split

# List of subjects to load
subjects = [f"AB29{str(i).zfill(2)}" for i in range(30, 36)]

# List of muscles as per the prompt (case-insensitive matching will be handled)
muscle_features = [
    "R_rectus femoris_EMG 1", "L_Rectus Femoris_EMG 1",
    "R_Vastus Lateralis_EMG 1", "L_Vastus Lateralis_EMG 1",
    "R_Semitendinosus_EMG 1", "L_Semitendinosus_EMG 1",
    "L_Biceps_Femoris_EMG 1",
    "R_Medial Gastrocnemius_EMG 1", "L_Medial Gastrocnemius_EMG 1",
    "R_Soleus_EMG 1", "L_Soleus_EMG 1",
    "R_Tibialis Anterior_EMG 1", "L_Tibialis Anterior_EMG 1",
    "R_Extensor Digitorum Brevis_EMG 1", "L_Extensor Digitorum Brevis_EMG 1"
]

def load_subject_data(subject):
    """
    Load and concatenate all trial CSV files from Raw Data and Processed Data folders for a subject.
    Returns a single DataFrame for the subject.
    """
    base_path = os.path.join("datasets", subject, subject)
    data_frames = []

    # Load from Raw Data and Processed Data folders
    for data_type in ["Raw Data", "Processed Data"]:
        data_path = os.path.join(base_path, data_type)
        if not os.path.exists(data_path):
            continue
        for root, _, files in os.walk(data_path):
            for file in files:
                if file.endswith(".csv"):
                    file_path = os.path.join(root, file)
                    try:
                        df = pd.read_csv(file_path)
                        data_frames.append(df)
                    except Exception as e:
                        print(f"Warning: Could not read {file_path}: {e}")

    if data_frames:
        subject_df = pd.concat(data_frames, ignore_index=True)
        return subject_df
    else:
        return pd.DataFrame()

def standardize_feature_names(df):
    """
    Standardize feature names to match muscle_features list ignoring case and underscores.
    """
    # Create a mapping from lower-case stripped names to actual columns
    col_map = {}
    for col in df.columns:
        col_key = col.lower().replace("_", "").replace(" ", "")
        col_map[col_key] = col

    standardized_cols = {}
    for feat in muscle_features:
        feat_key = feat.lower().replace("_", "").replace(" ", "")
        if feat_key in col_map:
            standardized_cols[feat] = col_map[feat_key]
        else:
            # Feature missing in this df
            standardized_cols[feat] = None
    return standardized_cols

def prepare_features_and_labels(df):
    """
    Extract features and labels from the dataframe.
    Fill missing features with zeros.
    Use 'Mode' or 'phase' as target label.
    """
    standardized_cols = standardize_feature_names(df)

    # Extract features
    feature_data = {}
    for feat, col_name in standardized_cols.items():
        if col_name and col_name in df.columns:
            feature_data[feat] = df[col_name]
        else:
            # Fill missing feature with zeros
            feature_data[feat] = np.zeros(len(df))

    X = pd.DataFrame(feature_data)

    # Determine target label column
    if 'Mode' in df.columns:
        y = df['Mode']
    elif 'phase' in df.columns:
        y = df['phase']
    else:
        raise ValueError("Neither 'Mode' nor 'phase' column found in data for target labels.")

    # Drop rows with NaN in target
    valid_idx = y.notna()
    X = X.loc[valid_idx].reset_index(drop=True)
    y = y.loc[valid_idx].reset_index(drop=True)

    return X, y

def main():
    # Load and concatenate data from all subjects
    all_data = []
    for subject in subjects:
        print(f"Loading data for subject {subject}...")
        subj_df = load_subject_data(subject)
        if subj_df.empty:
            print(f"Warning: No data found for subject {subject}")
            continue
        all_data.append(subj_df)

    if not all_data:
        print("No data loaded. Exiting.")
        return

    data_df = pd.concat(all_data, ignore_index=True)
    print(f"Total data samples loaded: {len(data_df)}")

    # Prepare features and labels
    try:
        X, y = prepare_features_and_labels(data_df)
    except ValueError as e:
        print(f"Error preparing features and labels: {e}")
        return

    print(f"Feature matrix shape: {X.shape}")
    print(f"Target vector length: {len(y)}")

    # Split data into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )

    # Train Random Forest Classifier
    print("Training Random Forest Classifier...")
    rf_clf = RandomForestClassifier(random_state=42)
    rf_clf.fit(X_train, y_train)
    y_pred_rf = rf_clf.predict(X_test)
    print("Random Forest Classification Report:")
    print(classification_report(y_test, y_pred_rf))

    # Train Support Vector Machine Classifier
    print("Training Support Vector Machine Classifier...")
    svm_clf = SVC(random_state=42)
    svm_clf.fit(X_train, y_train)
    y_pred_svm = svm_clf.predict(X_test)
    print("SVM Classification Report:")
    print(classification_report(y_test, y_pred_svm))

if __name__ == "__main__":
    main()
